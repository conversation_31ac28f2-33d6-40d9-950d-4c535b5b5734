/**
 * StatsService - Handles statistics and dynamic counters
 * Integrates with APIs to get real-time data
 */
class StatsService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.cacheDuration = 30 * 60 * 1000; // 30 minutos para stats
  }

  /**
   * Check if cache is valid
   */
  _isCacheValid(key) {
    if (!this.cacheExpiry.has(key)) return false;
    return Date.now() < this.cacheExpiry.get(key);
  }

  /**
   * Set cache with expiry
   */
  _setCache(key, data) {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.cacheDuration);
  }

  /**
   * Get cached data if valid
   */
  _getCache(key) {
    if (this._isCacheValid(key)) {
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * Get YouTube channel statistics
   */
  async getYouTubeStats() {
    const cacheKey = 'youtube_stats';
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      console.log('Returning YouTube stats from cache');
      return cachedData;
    }

    try {
      let stats;
      
      // Try API first
      if (window.apiService) {
        stats = await window.apiService.getYouTubeStats();
      } else {
        // Fallback to direct API call
        const response = await fetch('/api/stats/youtube');
        if (response.ok) {
          stats = await response.json();
        }
      }

      if (stats) {
        // Cache the result
        this._setCache(cacheKey, stats);
        return stats;
      }

      // Return default values if API fails
      return this.getDefaultYouTubeStats();
    } catch (error) {
      console.error('Error fetching YouTube stats:', error);
      return this.getDefaultYouTubeStats();
    }
  }

  /**
   * Get Spotify artist statistics
   */
  async getSpotifyStats() {
    const cacheKey = 'spotify_stats';
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      console.log('Returning Spotify stats from cache');
      return cachedData;
    }

    try {
      let stats;
      
      // Try API first
      if (window.apiService) {
        stats = await window.apiService.getSpotifyStats();
      } else {
        // Fallback to direct API call
        const response = await fetch('/api/stats/spotify');
        if (response.ok) {
          stats = await response.json();
        }
      }

      if (stats) {
        // Cache the result
        this._setCache(cacheKey, stats);
        return stats;
      }

      // Return default values if API fails
      return this.getDefaultSpotifyStats();
    } catch (error) {
      console.error('Error fetching Spotify stats:', error);
      return this.getDefaultSpotifyStats();
    }
  }

  /**
   * Get combined statistics for homepage counters
   */
  async getCombinedStats() {
    try {
      const [youtubeStats, spotifyStats] = await Promise.all([
        this.getYouTubeStats(),
        this.getSpotifyStats()
      ]);

      return {
        tracksCreated: spotifyStats.total_tracks || 40,
        monthlyListeners: youtubeStats.subscriber_count || 300,
        averageRating: 5.0,
        totalViews: youtubeStats.view_count || 0,
        totalAlbums: spotifyStats.total_albums || 0,
        channelTitle: youtubeStats.channel_title || 'IamSHIUBA'
      };
    } catch (error) {
      console.error('Error getting combined stats:', error);
      return this.getDefaultCombinedStats();
    }
  }

  /**
   * Update homepage counters with real data
   */
  async updateHomepageCounters() {
    try {
      const stats = await this.getCombinedStats();
      
      // Update tracks created counter
      const tracksCounter = document.querySelector('[data-translate-key="tracksCreated"]');
      if (tracksCounter) {
        tracksCounter.setAttribute('data-target', stats.tracksCreated.toString());
      }

      // Update monthly listeners counter (using subscriber count)
      const listenersCounter = document.querySelector('[data-target="300"]');
      if (listenersCounter) {
        listenersCounter.setAttribute('data-target', stats.monthlyListeners.toString());
      }

      console.log('Homepage counters updated with real data:', stats);
      
      // Trigger counter animation if Counter.js is available
      if (window.counterManager) {
        window.counterManager.restartCounters();
      }

      return stats;
    } catch (error) {
      console.error('Error updating homepage counters:', error);
      return null;
    }
  }

  /**
   * Default YouTube stats fallback
   */
  getDefaultYouTubeStats() {
    return {
      view_count: 10000,
      subscriber_count: 300,
      video_count: 25,
      channel_title: 'IamSHIUBA',
      subscriber_count_hidden: false
    };
  }

  /**
   * Default Spotify stats fallback
   */
  getDefaultSpotifyStats() {
    return {
      total_tracks: 40,
      total_albums: 8
    };
  }

  /**
   * Default combined stats fallback
   */
  getDefaultCombinedStats() {
    return {
      tracksCreated: 40,
      monthlyListeners: 300,
      averageRating: 5.0,
      totalViews: 10000,
      totalAlbums: 8,
      channelTitle: 'IamSHIUBA'
    };
  }

  /**
   * Format large numbers for display
   */
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  /**
   * Clear stats cache
   */
  clearCache() {
    this.cache.clear();
    this.cacheExpiry.clear();
    console.log('Stats cache cleared');
  }

  /**
   * Initialize stats service
   */
  async init() {
    console.log('StatsService initialized');
    
    // Update counters when page loads
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => this.updateHomepageCounters(), 1000);
      });
    } else {
      setTimeout(() => this.updateHomepageCounters(), 1000);
    }
  }
}

// Create global instance
window.statsService = new StatsService();

// Auto-initialize
window.statsService.init();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StatsService;
}
