/**
 * ApiService - Handles all API communications
 * Similar to Python services but for frontend
 */
class ApiService {
  constructor() {
    this.baseUrl = '/api';
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.cacheDuration = 60 * 60 * 1000; // 1 hora em ms
  }

  /**
   * Check if cache is valid
   */
  _isCacheValid(key) {
    if (!this.cacheExpiry.has(key)) return false;
    return Date.now() < this.cacheExpiry.get(key);
  }

  /**
   * Set cache with expiry
   */
  _setCache(key, data) {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.cacheDuration);
  }

  /**
   * Get cached data if valid
   */
  _getCache(key) {
    if (this._isCacheValid(key)) {
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * Generic API request method
   */
  async _request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Get YouTube playlists
   */
  async getYouTubePlaylists(searchQuery = '', maxResults = 50) {
    const cacheKey = `youtube_playlists_${searchQuery}_${maxResults}`;
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      console.log('Returning YouTube playlists from cache');
      return cachedData;
    }

    const params = new URLSearchParams({
      max_results: maxResults.toString()
    });
    
    if (searchQuery) {
      params.append('q', searchQuery);
    }

    try {
      const data = await this._request(`/playlists/youtube?${params}`);
      
      // Cache the result
      this._setCache(cacheKey, data.playlists || []);
      
      return data.playlists || [];
    } catch (error) {
      console.error('Error fetching YouTube playlists:', error);
      throw error;
    }
  }

  /**
   * Get Spotify playlists
   */
  async getSpotifyPlaylists(searchQuery = '', maxResults = 50) {
    const cacheKey = `spotify_playlists_${searchQuery}_${maxResults}`;
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      console.log('Returning Spotify playlists from cache');
      return cachedData;
    }

    const params = new URLSearchParams({
      max_results: maxResults.toString()
    });
    
    if (searchQuery) {
      params.append('q', searchQuery);
    }

    try {
      const data = await this._request(`/playlists/spotify?${params}`);
      
      // Cache the result
      this._setCache(cacheKey, data.playlists || []);
      
      return data.playlists || [];
    } catch (error) {
      console.error('Error fetching Spotify playlists:', error);
      throw error;
    }
  }

  /**
   * Get YouTube playlist details
   */
  async getYouTubePlaylistDetails(playlistId) {
    const cacheKey = `youtube_playlist_${playlistId}`;
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const data = await this._request(`/playlists/youtube/${playlistId}`);
      
      // Cache the result
      this._setCache(cacheKey, data.playlist);
      
      return data.playlist;
    } catch (error) {
      console.error(`Error fetching YouTube playlist ${playlistId}:`, error);
      throw error;
    }
  }

  /**
   * Get Spotify album details
   */
  async getSpotifyAlbumDetails(albumId) {
    const cacheKey = `spotify_album_${albumId}`;
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const data = await this._request(`/playlists/spotify/${albumId}`);
      
      // Cache the result
      this._setCache(cacheKey, data.playlist);
      
      return data.playlist;
    } catch (error) {
      console.error(`Error fetching Spotify album ${albumId}:`, error);
      throw error;
    }
  }

  /**
   * Get YouTube channel statistics
   */
  async getYouTubeStats() {
    const cacheKey = 'youtube_stats';
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const data = await this._request('/stats/youtube');
      
      // Cache the result
      this._setCache(cacheKey, data);
      
      return data;
    } catch (error) {
      console.error('Error fetching YouTube stats:', error);
      throw error;
    }
  }

  /**
   * Get Spotify artist statistics
   */
  async getSpotifyStats() {
    const cacheKey = 'spotify_stats';
    
    // Check cache first
    const cachedData = this._getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const data = await this._request('/stats/spotify');
      
      // Cache the result
      this._setCache(cacheKey, data);
      
      return data;
    } catch (error) {
      console.error('Error fetching Spotify stats:', error);
      throw error;
    }
  }

  /**
   * Get updates
   */
  async getUpdates() {
    const cacheKey = 'updates';
    
    // Check cache first (shorter cache for updates)
    if (this._isCacheValid(cacheKey)) {
      return this._getCache(cacheKey);
    }

    try {
      const data = await this._request('/updates');
      
      // Cache for 15 minutes
      this.cache.set(cacheKey, data.updates || []);
      this.cacheExpiry.set(cacheKey, Date.now() + (15 * 60 * 1000));
      
      return data.updates || [];
    } catch (error) {
      console.error('Error fetching updates:', error);
      throw error;
    }
  }

  /**
   * Clear all cache
   */
  clearCache() {
    this.cache.clear();
    this.cacheExpiry.clear();
    console.log('API cache cleared');
  }

  /**
   * Clear specific cache key
   */
  clearCacheKey(key) {
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    console.log(`Cache cleared for key: ${key}`);
  }
}

// Create global instance
window.apiService = new ApiService();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiService;
}
