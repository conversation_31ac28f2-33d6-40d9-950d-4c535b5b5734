/**
 * API Configuration
 * Centralized configuration for API endpoints and settings
 */
const API_CONFIG = {
  // Base URLs
  BASE_URL: '/api',
  
  // Endpoints
  ENDPOINTS: {
    // Playlists
    PLAYLISTS: '/playlists',
    YOUTUBE_PLAYLISTS: '/playlists/youtube',
    SPOTIFY_PLAYLISTS: '/playlists/spotify',
    
    // Playlist details
    YOUTUBE_PLAYLIST_DETAILS: (id) => `/playlists/youtube/${id}`,
    SPOTIFY_ALBUM_DETAILS: (id) => `/playlists/spotify/${id}`,
    
    // Statistics
    YOUTUBE_STATS: '/stats/youtube',
    SPOTIFY_STATS: '/stats/spotify',
    
    // Updates
    UPDATES: '/updates'
  },
  
  // Cache settings
  CACHE: {
    DEFAULT_DURATION: 60 * 60 * 1000, // 1 hour
    STATS_DURATION: 30 * 60 * 1000,   // 30 minutes
    UPDATES_DURATION: 15 * 60 * 1000  // 15 minutes
  },
  
  // Request settings
  REQUEST: {
    TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
  },
  
  // Default parameters
  DEFAULTS: {
    MAX_RESULTS: 50,
    ITEMS_PER_PAGE: 6
  },
  
  // Fallback data paths
  FALLBACK: {
    YOUTUBE_PLAYLISTS: './static/data/youtube-playlists.json',
    SPOTIFY_PLAYLISTS: './static/data/spotify-playlists.json'
  },
  
  // Feature flags
  FEATURES: {
    USE_API: true,           // Set to false to use only static JSON
    USE_CACHE: true,         // Enable/disable caching
    USE_FALLBACK: true,      // Enable fallback to static files
    ENABLE_STATS: true,      // Enable real-time statistics
    ENABLE_SEARCH: true,     // Enable search functionality
    ENABLE_PAGINATION: true  // Enable pagination
  },
  
  // Error messages
  ERRORS: {
    NETWORK_ERROR: 'Network error occurred. Please check your connection.',
    API_UNAVAILABLE: 'API service is currently unavailable.',
    INVALID_RESPONSE: 'Invalid response from server.',
    TIMEOUT: 'Request timed out. Please try again.',
    NOT_FOUND: 'Requested resource not found.',
    GENERIC: 'An unexpected error occurred.'
  }
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Development environment
  API_CONFIG.BASE_URL = 'http://localhost:5000/api';
  API_CONFIG.FEATURES.USE_API = false; // Use static files in development
} else if (window.location.hostname.includes('fly.dev')) {
  // Production environment (Fly.io)
  API_CONFIG.BASE_URL = '/api';
  API_CONFIG.FEATURES.USE_API = true;
}

// Make config globally available
window.API_CONFIG = API_CONFIG;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = API_CONFIG;
}
