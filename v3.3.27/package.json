{"name": "isstar-p-frontend", "version": "3.3.27", "description": "IamSHIUBA Artist <PERSON><PERSON><PERSON> - Frontend", "dependencies": {"@tailwindcss/cli": "^4.0.9", "flowbite": "^3.1.2", "sass": "1.89.2", "tailwindcss": "^4.0.9"}, "devDependencies": {"concurrently": "^8.2.2", "live-server": "^1.2.0"}, "scripts": {"build": "npx tailwindcss -i static/css/global.css -o ./static/css/output.css", "dev": "npx tailwindcss -i static/css/global.css -o ./static/css/output.css --watch", "serve": "live-server --port=3000 --host=localhost --open=/", "start": "concurrently \"npm run dev\" \"npm run serve\"", "build:prod": "npx tailwindcss -i static/css/global.css -o ./static/css/output.css --minify"}, "keywords": ["music", "artist", "portfolio", "streaming", "youtube", "spotify"], "author": "IamSHIUBA", "license": "MIT"}